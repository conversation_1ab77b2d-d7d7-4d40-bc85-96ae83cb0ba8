<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美好生活 App - 高保真原型展示</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: #fff;
        }

        .header h1 {
            font-size: 48px;
            font-weight: 800;
            margin-bottom: 10px;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 20px;
            opacity: 0.9;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        .prototype-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
            gap: 40px;
            max-width: 1600px;
            margin: 0 auto;
            padding-bottom: 40px;
        }

        .prototype-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .prototype-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            text-align: center;
            margin-bottom: 20px;
            color: #fff;
        }

        .card-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .card-description {
            font-size: 16px;
            opacity: 0.9;
            line-height: 1.4;
        }

        .prototype-frame {
            width: 100%;
            height: 800px;
            border: none;
            border-radius: 15px;
            background: #fff;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .coming-soon {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 800px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            color: #fff;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            flex-direction: column;
            gap: 15px;
        }

        .coming-soon i {
            font-size: 48px;
            opacity: 0.8;
        }

        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .nav-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: #fff;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .nav-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .page-info {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            color: rgba(255, 255, 255, 0.9);
            font-size: 14px;
            line-height: 1.5;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            margin-left: 10px;
        }

        .status-complete {
            background: rgba(76, 175, 80, 0.8);
            color: #fff;
        }

        .status-coming {
            background: rgba(255, 193, 7, 0.8);
            color: #fff;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .prototype-grid {
                grid-template-columns: 1fr;
                gap: 30px;
                max-width: 100%;
            }

            .prototype-frame {
                height: 700px;
            }

            .header h1 {
                font-size: 36px;
            }

            .header p {
                font-size: 18px;
            }

            .navigation {
                position: static;
                text-align: center;
                margin-bottom: 30px;
            }
        }

        @media (max-width: 480px) {
            .prototype-frame {
                height: 600px;
            }

            .prototype-grid {
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="navigation">
        <a href="#" class="nav-button" onclick="scrollToTop()">
            <i class="fas fa-arrow-up"></i>
            返回顶部
        </a>
    </div>

    <div class="header">
        <h1><i class="fas fa-mobile-alt"></i> 美好生活 App</h1>
        <p>高保真移动应用原型展示 - iPhone 15 Pro 设计规范</p>
    </div>

    <div class="prototype-grid">
        <!-- 启动加载界面 -->
        <div class="prototype-card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-sun"></i>
                    启动加载界面
                    <span class="status-badge status-complete">已完成</span>
                </h2>
                <p class="card-description">展示东南亚海边晒太阳的美好愿景，包含品牌元素和加载动画</p>
            </div>
            <div class="page-info">
                <strong>设计特点：</strong><br>
                • 使用真实的海滩背景图片（来自Unsplash）<br>
                • 渐变色彩过渡动画<br>
                • 浮动图标动效<br>
                • iOS状态栏模拟<br>
                • 品牌Logo和加载指示器
            </div>
            <iframe src="pages/splash.html" class="prototype-frame"></iframe>
        </div>

        <!-- AI对话主界面 -->
        <div class="prototype-card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-comments"></i>
                    AI对话主界面
                    <span class="status-badge status-complete">已完成</span>
                </h2>
                <p class="card-description">类似DeepSeek的AI对话界面，帮助用户实现生活愿景</p>
            </div>
            <div class="page-info">
                <strong>功能特点：</strong><br>
                • 模拟真实的AI对话体验<br>
                • 愿景规划和步骤分解<br>
                • 待办事项管理<br>
                • 打字动画和交互效果<br>
                • 完整的对话历史展示
            </div>
            <iframe src="pages/home.html" class="prototype-frame"></iframe>
        </div>

        <!-- 我的愿景页面 -->
        <div class="prototype-card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-heart"></i>
                    我的愿景
                    <span class="status-badge status-complete">已完成</span>
                </h2>
                <p class="card-description">愿景列表管理，展示所有愿景的概览和进度</p>
            </div>
            <div class="page-info">
                <strong>功能特点：</strong><br>
                • 愿景统计概览<br>
                • 愿景卡片列表<br>
                • 进度可视化<br>
                • 状态标签管理<br>
                • 搜索和筛选功能
            </div>
            <iframe src="pages/my-visions.html" class="prototype-frame"></iframe>
        </div>

        <!-- 愿景详情页面 -->
        <div class="prototype-card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-info-circle"></i>
                    愿景详情
                    <span class="status-badge status-complete">已完成</span>
                </h2>
                <p class="card-description">单个愿景的详细信息，包含实现步骤和进度追踪</p>
            </div>
            <div class="page-info">
                <strong>功能特点：</strong><br>
                • 愿景概览卡片<br>
                • 详细实现步骤<br>
                • 进度可视化<br>
                • 时间节点管理<br>
                • 编辑和分享功能
            </div>
            <iframe src="pages/vision-detail.html" class="prototype-frame"></iframe>
        </div>

        <!-- 数据洞察页面 -->
        <div class="prototype-card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-brain"></i>
                    数据洞察
                    <span class="status-badge status-complete">已完成</span>
                </h2>
                <p class="card-description">智能数据分析和AI洞察建议，了解成长轨迹</p>
            </div>
            <div class="page-info">
                <strong>功能特点：</strong><br>
                • 关键指标展示<br>
                • 时间范围切换<br>
                • 周进度图表<br>
                • AI智能建议<br>
                • 成长轨迹分析
            </div>
            <iframe src="pages/insights.html" class="prototype-frame"></iframe>
        </div>



        <!-- 设置页面 -->
        <div class="prototype-card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-cog"></i>
                    设置
                    <span class="status-badge status-complete">已完成</span>
                </h2>
                <p class="card-description">应用设置和偏好配置页面</p>
            </div>
            <div class="page-info">
                <strong>功能特点：</strong><br>
                • 用户信息管理<br>
                • 通知和隐私设置<br>
                • 外观和语言选择<br>
                • 帮助和支持<br>
                • 版本信息展示
            </div>
            <iframe src="pages/settings.html" class="prototype-frame"></iframe>
        </div>

        <!-- 新建愿景页面 - 即将推出 -->
        <div class="prototype-card">
            <div class="card-header">
                <h2 class="card-title">
                    <i class="fas fa-plus-circle"></i>
                    新建愿景
                    <span class="status-badge status-coming">即将推出</span>
                </h2>
                <p class="card-description">创建新的人生愿景和目标规划</p>
            </div>
            <div class="page-info">
                <strong>计划功能：</strong><br>
                • 愿景描述输入<br>
                • 目标时间设定<br>
                • 资源需求评估<br>
                • AI智能建议<br>
                • 计划生成
            </div>
            <div class="coming-soon">
                <i class="fas fa-lightbulb"></i>
                <div>
                    <div>即将推出</div>
                    <div style="font-size: 14px; opacity: 0.8; margin-top: 5px;">新建愿景页面规划中</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.prototype-card');
            
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.2}s`;
                card.style.animation = 'fadeInUp 0.8s ease-out forwards';
            });
        });

        // CSS动画定义
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
