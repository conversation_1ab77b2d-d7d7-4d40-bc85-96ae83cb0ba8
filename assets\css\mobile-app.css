/* Mobile App Prototype Global Styles */
/* iPhone 15 Pro Dimensions: 393 x 852 px */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    background: #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

/* iPhone 15 Pro Container */
.iphone-container {
    width: 393px;
    height: 852px;
    background: #000;
    border-radius: 47px;
    padding: 8px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    position: relative;
}

.iphone-screen {
    width: 100%;
    height: 100%;
    background: #fff;
    border-radius: 39px;
    overflow: hidden;
    position: relative;
}

/* iOS Status Bar */
.status-bar {
    height: 54px;
    background: transparent;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    color: #fff;
    font-size: 17px;
    font-weight: 600;
}

.status-left {
    flex: 1;
    display: flex;
    align-items: center;
}

.time {
    font-size: 17px;
    font-weight: 600;
    letter-spacing: -0.3px;
}

.status-center {
    flex: 0 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dynamic-island {
    width: 126px;
    height: 37px;
    background: rgba(0, 0, 0, 0.85);
    border-radius: 19px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
}

.status-icons {
    display: flex;
    align-items: center;
    gap: 6px;
}

.cellular-signal {
    display: flex;
    gap: 2px;
    align-items: flex-end;
}

.signal-dot {
    width: 3px;
    height: 3px;
    background: #fff;
    border-radius: 50%;
    opacity: 0.6;
}

.signal-dot:nth-child(1) {
    height: 4px;
    opacity: 1;
}
.signal-dot:nth-child(2) {
    height: 6px;
    opacity: 1;
}
.signal-dot:nth-child(3) {
    height: 8px;
    opacity: 1;
}
.signal-dot:nth-child(4) {
    height: 10px;
    opacity: 0.4;
}

.wifi-signal {
    color: #fff;
    opacity: 0.9;
}

.battery-indicator {
    display: flex;
    align-items: center;
    gap: 1px;
}

.battery-body {
    width: 24px;
    height: 12px;
    border: 1px solid rgba(255, 255, 255, 0.6);
    border-radius: 2px;
    position: relative;
    background: transparent;
}

.battery-level {
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    background: #fff;
    border-radius: 1px;
    width: 70%; /* 70% 电量 */
}

.battery-tip {
    width: 2px;
    height: 6px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 0 1px 1px 0;
}

/* App Content Area */
.app-content {
    height: 100%;
    width: 100%;
    position: relative;
    overflow: hidden;
}

/* Loading Animation */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-top: 3px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 1.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Slide Up Animation */
.slide-up {
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Pulse Animation */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* Gradient Backgrounds */
.gradient-sunset {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

.gradient-ocean {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-tropical {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* Text Styles */
.app-title {
    font-size: 32px;
    font-weight: 700;
    color: #fff;
    text-align: center;
    margin-bottom: 8px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.app-subtitle {
    font-size: 18px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.9);
    text-align: center;
    margin-bottom: 40px;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.2);
}

/* Button Styles */
.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    padding: 16px 32px;
    border-radius: 25px;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Responsive adjustments for smaller screens */
@media (max-width: 450px) {
    .iphone-container {
        width: 100%;
        max-width: 393px;
        height: 100vh;
        max-height: 852px;
        border-radius: 0;
        padding: 0;
        box-shadow: none;
    }
    
    .iphone-screen {
        border-radius: 0;
    }
}
