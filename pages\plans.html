<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的计划 - 美好生活</title>
    <link rel="stylesheet" href="../assets/css/mobile-app.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .plans-container {
            height: 100%;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 顶部导航栏 */
        .nav-header {
            background: #fff;
            height: 88px;
            padding: 54px 20px 16px;
            border-bottom: 1px solid #e5e5e7;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 100;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: #e5e5ea;
            transform: scale(1.05);
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
        }

        .nav-actions {
            display: flex;
            gap: 8px;
        }

        .nav-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .nav-btn:hover {
            background: #e5e5ea;
            transform: scale(1.05);
        }

        /* 主内容区域 */
        .plans-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        /* 愿景卡片 */
        .vision-card {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 24px;
            color: #fff;
            box-shadow: 0 8px 32px rgba(255, 154, 158, 0.3);
        }

        .vision-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .vision-description {
            font-size: 14px;
            line-height: 1.5;
            opacity: 0.9;
            margin-bottom: 16px;
        }

        .vision-stats {
            display: flex;
            gap: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 计划步骤 */
        .plan-section {
            background: #fff;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .progress-badge {
            background: #34c759;
            color: #fff;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .progress-badge.in-progress {
            background: #ff9500;
        }

        .progress-badge.pending {
            background: #8e8e93;
        }

        /* 步骤列表 */
        .steps-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .step-item {
            display: flex;
            align-items: flex-start;
            padding: 16px 0;
            border-bottom: 1px solid #f2f2f7;
        }

        .step-item:last-child {
            border-bottom: none;
        }

        .step-number {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            color: #8e8e93;
            margin-right: 16px;
            flex-shrink: 0;
        }

        .step-number.completed {
            background: #34c759;
            color: #fff;
        }

        .step-number.current {
            background: #007aff;
            color: #fff;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 4px;
        }

        .step-description {
            font-size: 14px;
            color: #8e8e93;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .step-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            color: #8e8e93;
        }

        .step-deadline {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .step-progress {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* 进度条 */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f2f2f7;
            border-radius: 4px;
            overflow: hidden;
            margin: 16px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007aff 0%, #34c759 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* 快速操作按钮 */
        .quick-actions {
            position: fixed;
            bottom: 34px;
            right: 20px;
            z-index: 100;
        }

        .fab {
            width: 56px;
            height: 56px;
            border-radius: 28px;
            background: #007aff;
            border: none;
            color: #fff;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 8px 32px rgba(0, 122, 255, 0.4);
            transition: all 0.3s ease;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 40px rgba(0, 122, 255, 0.6);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-frame">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="time">9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <div class="wifi-icon">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div class="battery">
                        <div class="battery-level"></div>
                    </div>
                </div>
            </div>

            <!-- App Content -->
            <div class="app-content">
                <div class="plans-container">
                    <!-- 导航栏 -->
                    <div class="nav-header">
                        <div class="nav-left">
                            <button class="back-btn" onclick="history.back()" title="返回">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                            <div class="nav-title">我的计划</div>
                        </div>
                        <div class="nav-actions">
                            <button class="nav-btn" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="nav-btn" title="分享">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 主内容 -->
                    <div class="plans-content">
                        <!-- 愿景概览 -->
                        <div class="vision-card">
                            <div class="vision-title">
                                🏖️ 东南亚海边生活愿景
                            </div>
                            <div class="vision-description">
                                在30岁前在泰国普吉岛或马来西亚槟城购买海景房，实现远程工作的自由生活方式
                            </div>
                            <div class="vision-stats">
                                <div class="stat-item">
                                    <span class="stat-number">6-7</span>
                                    <span class="stat-label">年计划</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">42%</span>
                                    <span class="stat-label">完成度</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-number">3/7</span>
                                    <span class="stat-label">步骤</span>
                                </div>
                            </div>
                        </div>

                        <!-- 整体进度 -->
                        <div class="plan-section">
                            <div class="section-header">
                                <div class="section-title">
                                    <i class="fas fa-chart-line"></i>
                                    整体进度
                                </div>
                                <div class="progress-badge in-progress">进行中</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 42%"></div>
                            </div>
                            <p style="font-size: 14px; color: #8e8e93; margin: 8px 0 0;">
                                已完成 3 个关键步骤，还有 4 个步骤待完成
                            </p>
                        </div>

                        <!-- 详细步骤 -->
                        <div class="plan-section">
                            <div class="section-header">
                                <div class="section-title">
                                    <i class="fas fa-list-check"></i>
                                    实现步骤
                                </div>
                            </div>
                            <ul class="steps-list">
                                <li class="step-item">
                                    <div class="step-number completed">✓</div>
                                    <div class="step-content">
                                        <div class="step-title">提升技能和收入</div>
                                        <div class="step-description">学习新技术栈，争取加薪或跳槽到更高薪职位</div>
                                        <div class="step-meta">
                                            <div class="step-deadline">
                                                <i class="fas fa-calendar"></i>
                                                已完成
                                            </div>
                                            <div class="step-progress">
                                                <i class="fas fa-check-circle"></i>
                                                100%
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="step-item">
                                    <div class="step-number completed">✓</div>
                                    <div class="step-content">
                                        <div class="step-title">建立应急基金</div>
                                        <div class="step-description">存储6个月生活费作为应急基金</div>
                                        <div class="step-meta">
                                            <div class="step-deadline">
                                                <i class="fas fa-calendar"></i>
                                                已完成
                                            </div>
                                            <div class="step-progress">
                                                <i class="fas fa-check-circle"></i>
                                                100%
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="step-item">
                                    <div class="step-number current">3</div>
                                    <div class="step-content">
                                        <div class="step-title">学习理财投资</div>
                                        <div class="step-description">学习基金、股票等投资知识，开始小额投资实践</div>
                                        <div class="step-meta">
                                            <div class="step-deadline">
                                                <i class="fas fa-calendar"></i>
                                                2024年12月
                                            </div>
                                            <div class="step-progress">
                                                <i class="fas fa-clock"></i>
                                                65%
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="step-item">
                                    <div class="step-number">4</div>
                                    <div class="step-content">
                                        <div class="step-title">积累首付资金</div>
                                        <div class="step-description">通过工作收入和投资收益积累房产首付</div>
                                        <div class="step-meta">
                                            <div class="step-deadline">
                                                <i class="fas fa-calendar"></i>
                                                2027年6月
                                            </div>
                                            <div class="step-progress">
                                                <i class="fas fa-hourglass-half"></i>
                                                待开始
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="step-item">
                                    <div class="step-number">5</div>
                                    <div class="step-content">
                                        <div class="step-title">实地考察房产</div>
                                        <div class="step-description">前往泰国和马来西亚实地考察合适的海景房产</div>
                                        <div class="step-meta">
                                            <div class="step-deadline">
                                                <i class="fas fa-calendar"></i>
                                                2028年3月
                                            </div>
                                            <div class="step-progress">
                                                <i class="fas fa-hourglass-half"></i>
                                                待开始
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="step-item">
                                    <div class="step-number">6</div>
                                    <div class="step-content">
                                        <div class="step-title">办理相关手续</div>
                                        <div class="step-description">了解外国人购房政策，准备相关法律文件</div>
                                        <div class="step-meta">
                                            <div class="step-deadline">
                                                <i class="fas fa-calendar"></i>
                                                2029年6月
                                            </div>
                                            <div class="step-progress">
                                                <i class="fas fa-hourglass-half"></i>
                                                待开始
                                            </div>
                                        </div>
                                    </div>
                                </li>
                                <li class="step-item">
                                    <div class="step-number">7</div>
                                    <div class="step-content">
                                        <div class="step-title">完成购房和搬迁</div>
                                        <div class="step-description">完成房产购买，安排搬迁和远程工作设置</div>
                                        <div class="step-meta">
                                            <div class="step-deadline">
                                                <i class="fas fa-calendar"></i>
                                                2030年前
                                            </div>
                                            <div class="step-progress">
                                                <i class="fas fa-hourglass-half"></i>
                                                待开始
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="quick-actions">
                        <button class="fab" title="添加新步骤">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
