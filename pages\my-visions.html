<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的愿景 - 美好生活</title>
    <link rel="stylesheet" href="../assets/css/mobile-app.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .visions-container {
            height: 100%;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 顶部导航栏 */
        .nav-header {
            background: #fff;
            height: 88px;
            padding: 54px 20px 16px;
            border-bottom: 1px solid #e5e5e7;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 100;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: #e5e5ea;
            transform: scale(1.05);
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
        }

        .nav-actions {
            display: flex;
            gap: 8px;
        }

        .nav-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .nav-btn:hover {
            background: #e5e5ea;
            transform: scale(1.05);
        }

        /* 主内容区域 */
        .visions-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        /* 统计概览 */
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #fff;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 20px;
            color: #fff;
        }

        .stat-icon.total {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-icon.active {
            background: linear-gradient(135deg, #007aff 0%, #34c759 100%);
        }

        .stat-icon.completed {
            background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
        }

        .stat-icon.progress {
            background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #1d1d1f;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #8e8e93;
        }

        /* 愿景列表 */
        .visions-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .vision-item {
            background: #fff;
            border-radius: 16px;
            margin-bottom: 16px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
        }

        .vision-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        }

        .vision-header {
            position: relative;
            height: 120px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            overflow: hidden;
        }

        .vision-header.beach {
            background: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80') center/cover;
        }

        .vision-header.travel {
            background: url('https://images.unsplash.com/photo-1488646953014-85cb44e25828?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80') center/cover;
        }

        .vision-header.business {
            background: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80') center/cover;
        }

        .vision-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            z-index: 1;
        }

        .vision-emoji {
            font-size: 48px;
            position: relative;
            z-index: 2;
        }

        .vision-content {
            padding: 20px;
        }

        .vision-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .vision-status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .vision-status.active {
            background: #e3f2fd;
            color: #1976d2;
        }

        .vision-status.completed {
            background: #e8f5e8;
            color: #2e7d32;
        }

        .vision-status.paused {
            background: #fff3e0;
            color: #f57c00;
        }

        .vision-description {
            font-size: 14px;
            color: #8e8e93;
            line-height: 1.4;
            margin-bottom: 16px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .vision-progress {
            margin-bottom: 16px;
        }

        .progress-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .progress-label {
            font-size: 14px;
            color: #1d1d1f;
            font-weight: 600;
        }

        .progress-percentage {
            font-size: 14px;
            color: #007aff;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #f2f2f7;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007aff 0%, #34c759 100%);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .vision-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #8e8e93;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* 快速操作 */
        .quick-actions {
            position: fixed;
            bottom: 34px;
            right: 20px;
            z-index: 100;
        }

        .fab {
            width: 56px;
            height: 56px;
            border-radius: 28px;
            background: #007aff;
            border: none;
            color: #fff;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 8px 32px rgba(0, 122, 255, 0.4);
            transition: all 0.3s ease;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 40px rgba(0, 122, 255, 0.6);
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #8e8e93;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .empty-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
            color: #1d1d1f;
        }

        .empty-description {
            font-size: 16px;
            line-height: 1.4;
            margin-bottom: 24px;
        }

        .empty-action {
            background: #007aff;
            color: #fff;
            border: none;
            padding: 12px 24px;
            border-radius: 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .empty-action:hover {
            background: #0056b3;
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-frame">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="time">9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <div class="wifi-icon">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div class="battery">
                        <div class="battery-level"></div>
                    </div>
                </div>
            </div>

            <!-- App Content -->
            <div class="app-content">
                <div class="visions-container">
                    <!-- 导航栏 -->
                    <div class="nav-header">
                        <div class="nav-left">
                            <button class="back-btn" onclick="history.back()" title="返回">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                            <div class="nav-title">我的愿景</div>
                        </div>
                        <div class="nav-actions">
                            <button class="nav-btn" title="搜索">
                                <i class="fas fa-search"></i>
                            </button>
                            <button class="nav-btn" title="筛选">
                                <i class="fas fa-filter"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 主内容 -->
                    <div class="visions-content">
                        <!-- 统计概览 -->
                        <div class="stats-overview">
                            <div class="stat-card">
                                <div class="stat-icon total">
                                    <i class="fas fa-heart"></i>
                                </div>
                                <div class="stat-number">3</div>
                                <div class="stat-label">总愿景</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon active">
                                    <i class="fas fa-play"></i>
                                </div>
                                <div class="stat-number">2</div>
                                <div class="stat-label">进行中</div>
                            </div>
                        </div>

                        <!-- 愿景列表 -->
                        <ul class="visions-list">
                            <li class="vision-item" onclick="window.open('vision-detail.html', '_blank')">
                                <div class="vision-header beach">
                                    <div class="vision-emoji">🏖️</div>
                                </div>
                                <div class="vision-content">
                                    <div class="vision-title">
                                        东南亚海边生活
                                        <span class="vision-status active">进行中</span>
                                    </div>
                                    <div class="vision-description">
                                        在30岁前在泰国或马来西亚海边拥有一套房子，实现远程工作的自由生活方式，每天可以在海边看日出日落。
                                    </div>
                                    <div class="vision-progress">
                                        <div class="progress-header">
                                            <span class="progress-label">实现进度</span>
                                            <span class="progress-percentage">42%</span>
                                        </div>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 42%"></div>
                                        </div>
                                    </div>
                                    <div class="vision-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>目标：2030年前</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-tasks"></i>
                                            <span>3/7 步骤</span>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="vision-item">
                                <div class="vision-header travel">
                                    <div class="vision-emoji">✈️</div>
                                </div>
                                <div class="vision-content">
                                    <div class="vision-title">
                                        环游世界计划
                                        <span class="vision-status paused">暂停中</span>
                                    </div>
                                    <div class="vision-description">
                                        在35岁前游遍世界七大洲，体验不同文化，记录美好瞬间，丰富人生阅历。
                                    </div>
                                    <div class="vision-progress">
                                        <div class="progress-header">
                                            <span class="progress-label">实现进度</span>
                                            <span class="progress-percentage">15%</span>
                                        </div>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 15%"></div>
                                        </div>
                                    </div>
                                    <div class="vision-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>目标：2035年前</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-map-marker-alt"></i>
                                            <span>已访问 2/7 洲</span>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="vision-item">
                                <div class="vision-header business">
                                    <div class="vision-emoji">💼</div>
                                </div>
                                <div class="vision-content">
                                    <div class="vision-title">
                                        创业梦想
                                        <span class="vision-status active">进行中</span>
                                    </div>
                                    <div class="vision-description">
                                        创建一家有社会价值的科技公司，解决实际问题，实现财务自由和个人价值。
                                    </div>
                                    <div class="vision-progress">
                                        <div class="progress-header">
                                            <span class="progress-label">实现进度</span>
                                            <span class="progress-percentage">28%</span>
                                        </div>
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 28%"></div>
                                        </div>
                                    </div>
                                    <div class="vision-meta">
                                        <div class="meta-item">
                                            <i class="fas fa-calendar"></i>
                                            <span>目标：2028年前</span>
                                        </div>
                                        <div class="meta-item">
                                            <i class="fas fa-lightbulb"></i>
                                            <span>概念验证阶段</span>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <!-- 快速操作 -->
                    <div class="quick-actions">
                        <button class="fab" title="新建愿景">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
