<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进度追踪 - 美好生活</title>
    <link rel="stylesheet" href="../assets/css/mobile-app.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .progress-container {
            height: 100%;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 顶部导航栏 */
        .nav-header {
            background: #fff;
            height: 88px;
            padding: 54px 20px 16px;
            border-bottom: 1px solid #e5e5e7;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 100;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: #e5e5ea;
            transform: scale(1.05);
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
        }

        /* 主内容区域 */
        .progress-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 24px;
        }

        .stat-card {
            background: #fff;
            border-radius: 16px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            font-size: 20px;
            color: #fff;
        }

        .stat-icon.progress {
            background: linear-gradient(135deg, #007aff 0%, #34c759 100%);
        }

        .stat-icon.time {
            background: linear-gradient(135deg, #ff9500 0%, #ff6b35 100%);
        }

        .stat-icon.money {
            background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
        }

        .stat-icon.tasks {
            background: linear-gradient(135deg, #af52de 0%, #ff2d92 100%);
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #1d1d1f;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #8e8e93;
        }

        /* 进度图表 */
        .chart-section {
            background: #fff;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 圆形进度条 */
        .circular-progress {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto 20px;
        }

        .progress-ring {
            transform: rotate(-90deg);
        }

        .progress-ring-bg {
            fill: none;
            stroke: #f2f2f7;
            stroke-width: 8;
        }

        .progress-ring-fill {
            fill: none;
            stroke: url(#progressGradient);
            stroke-width: 8;
            stroke-linecap: round;
            transition: stroke-dasharray 0.5s ease;
        }

        .progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .progress-percentage {
            font-size: 24px;
            font-weight: 700;
            color: #1d1d1f;
        }

        .progress-subtitle {
            font-size: 12px;
            color: #8e8e93;
        }

        /* 里程碑时间线 */
        .timeline {
            position: relative;
            padding-left: 32px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 16px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #f2f2f7;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 24px;
        }

        .timeline-item::before {
            content: '';
            position: absolute;
            left: -24px;
            top: 8px;
            width: 16px;
            height: 16px;
            border-radius: 8px;
            background: #f2f2f7;
            border: 3px solid #fff;
            box-shadow: 0 0 0 2px #f2f2f7;
        }

        .timeline-item.completed::before {
            background: #34c759;
            box-shadow: 0 0 0 2px #34c759;
        }

        .timeline-item.current::before {
            background: #007aff;
            box-shadow: 0 0 0 2px #007aff;
        }

        .timeline-date {
            font-size: 12px;
            color: #8e8e93;
            margin-bottom: 4px;
        }

        .timeline-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 4px;
        }

        .timeline-description {
            font-size: 14px;
            color: #8e8e93;
            line-height: 1.4;
        }

        /* 资金追踪 */
        .money-tracker {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(135deg, #34c759 0%, #30d158 100%);
            border-radius: 16px;
            padding: 20px;
            color: #fff;
            margin-bottom: 20px;
        }

        .money-info h3 {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .money-amount {
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .money-target {
            font-size: 14px;
            opacity: 0.8;
        }

        .money-icon {
            font-size: 48px;
            opacity: 0.3;
        }

        /* 月度进展 */
        .monthly-progress {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 12px;
        }

        .month-item {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
        }

        .month-item.current {
            background: #007aff;
            color: #fff;
        }

        .month-name {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .month-progress {
            font-size: 18px;
            font-weight: 700;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-frame">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="time">9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <div class="wifi-icon">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div class="battery">
                        <div class="battery-level"></div>
                    </div>
                </div>
            </div>

            <!-- App Content -->
            <div class="app-content">
                <div class="progress-container">
                    <!-- 导航栏 -->
                    <div class="nav-header">
                        <div class="nav-left">
                            <button class="back-btn" onclick="history.back()" title="返回">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                            <div class="nav-title">进度追踪</div>
                        </div>
                    </div>

                    <!-- 主内容 -->
                    <div class="progress-content">
                        <!-- 统计概览 -->
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon progress">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <div class="stat-number">42%</div>
                                <div class="stat-label">总体进度</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon time">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div class="stat-number">5.2年</div>
                                <div class="stat-label">剩余时间</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon money">
                                    <i class="fas fa-dollar-sign"></i>
                                </div>
                                <div class="stat-number">28万</div>
                                <div class="stat-label">已储蓄</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon tasks">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <div class="stat-number">3/7</div>
                                <div class="stat-label">完成步骤</div>
                            </div>
                        </div>

                        <!-- 整体进度图表 -->
                        <div class="chart-section">
                            <div class="section-title">
                                <i class="fas fa-chart-pie"></i>
                                整体完成度
                            </div>
                            <div class="circular-progress">
                                <svg width="120" height="120" class="progress-ring">
                                    <defs>
                                        <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#007aff"/>
                                            <stop offset="100%" style="stop-color:#34c759"/>
                                        </linearGradient>
                                    </defs>
                                    <circle cx="60" cy="60" r="52" class="progress-ring-bg"/>
                                    <circle cx="60" cy="60" r="52" class="progress-ring-fill" 
                                            stroke-dasharray="137.5 327.3"/>
                                </svg>
                                <div class="progress-text">
                                    <div class="progress-percentage">42%</div>
                                    <div class="progress-subtitle">已完成</div>
                                </div>
                            </div>
                            <p style="text-align: center; font-size: 14px; color: #8e8e93;">
                                按当前进度，预计 2030年3月 完成目标
                            </p>
                        </div>

                        <!-- 资金追踪 -->
                        <div class="money-tracker">
                            <div class="money-info">
                                <h3>储蓄进度</h3>
                                <div class="money-amount">¥280,000</div>
                                <div class="money-target">目标: ¥800,000 (35%)</div>
                            </div>
                            <div class="money-icon">
                                <i class="fas fa-piggy-bank"></i>
                            </div>
                        </div>

                        <!-- 关键里程碑 -->
                        <div class="chart-section">
                            <div class="section-title">
                                <i class="fas fa-flag"></i>
                                关键里程碑
                            </div>
                            <div class="timeline">
                                <div class="timeline-item completed">
                                    <div class="timeline-date">2024年6月</div>
                                    <div class="timeline-title">技能提升完成</div>
                                    <div class="timeline-description">成功学习新技术栈，月薪提升至3万</div>
                                </div>
                                <div class="timeline-item completed">
                                    <div class="timeline-date">2024年9月</div>
                                    <div class="timeline-title">应急基金建立</div>
                                    <div class="timeline-description">成功储蓄18万应急基金</div>
                                </div>
                                <div class="timeline-item current">
                                    <div class="timeline-date">2024年12月</div>
                                    <div class="timeline-title">投资学习阶段</div>
                                    <div class="timeline-description">正在学习理财投资知识，已完成65%</div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-date">2027年6月</div>
                                    <div class="timeline-title">首付资金达标</div>
                                    <div class="timeline-description">积累足够的房产首付资金</div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-date">2028年3月</div>
                                    <div class="timeline-title">实地考察</div>
                                    <div class="timeline-description">前往东南亚考察合适房产</div>
                                </div>
                                <div class="timeline-item">
                                    <div class="timeline-date">2030年前</div>
                                    <div class="timeline-title">目标达成</div>
                                    <div class="timeline-description">完成购房，实现海边生活愿景</div>
                                </div>
                            </div>
                        </div>

                        <!-- 月度进展 -->
                        <div class="chart-section">
                            <div class="section-title">
                                <i class="fas fa-calendar-alt"></i>
                                近期月度进展
                            </div>
                            <div class="monthly-progress">
                                <div class="month-item">
                                    <div class="month-name">10月</div>
                                    <div class="month-progress">38%</div>
                                </div>
                                <div class="month-item">
                                    <div class="month-name">11月</div>
                                    <div class="month-progress">40%</div>
                                </div>
                                <div class="month-item current">
                                    <div class="month-name">12月</div>
                                    <div class="month-progress">42%</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
