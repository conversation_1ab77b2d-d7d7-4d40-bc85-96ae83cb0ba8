<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据洞察 - 美好生活</title>
    <link rel="stylesheet" href="../assets/css/mobile-app.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .insights-container {
            height: 100%;
            background: linear-gradient(to bottom, #f8f4ff, #ffffff);
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 顶部导航栏 */
        .nav-header {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
            height: 88px;
            padding: 54px 20px 16px;
            border-bottom: 1px solid #e8e0ff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 100;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: #e5e5ea;
            transform: scale(1.05);
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
        }

        .nav-subtitle {
            font-size: 12px;
            color: #8b5cf6;
            margin-top: 2px;
        }

        /* 时间范围选择器 */
        .time-selector {
            display: flex;
            gap: 8px;
            margin-top: 12px;
        }

        .time-btn {
            padding: 6px 12px;
            border-radius: 8px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid #e5e7eb;
            background: #fff;
            color: #6b7280;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .time-btn.active {
            background: #8b5cf6;
            color: #fff;
            border-color: #8b5cf6;
        }

        .time-btn:hover:not(.active) {
            background: #f3f4f6;
        }

        /* 主内容区域 */
        .insights-content {
            flex: 1;
            overflow-y: auto;
            padding: 16px;
        }

        /* 关键指标网格 */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 16px;
        }

        .metric-card {
            background: #fff;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.2s ease;
        }

        .metric-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .metric-card.green {
            border-left: 4px solid #10b981;
        }

        .metric-card.blue {
            border-left: 4px solid #3b82f6;
        }

        .metric-card.orange {
            border-left: 4px solid #f59e0b;
        }

        .metric-card.purple {
            border-left: 4px solid #8b5cf6;
        }

        .metric-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
        }

        .metric-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #fff;
        }

        .metric-icon.green {
            background: #10b981;
        }

        .metric-icon.blue {
            background: #3b82f6;
        }

        .metric-icon.orange {
            background: #f59e0b;
        }

        .metric-icon.purple {
            background: #8b5cf6;
        }

        .metric-info {
            flex: 1;
        }

        .metric-label {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 2px;
        }

        .metric-value {
            font-size: 18px;
            font-weight: 700;
            color: #1f2937;
        }

        /* 图表卡片 */
        .chart-card {
            background: #fff;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        }

        .chart-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .chart-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
        }

        .chart-icon {
            color: #8b5cf6;
        }

        /* 周进度条 */
        .weekly-progress {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .day-progress {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .day-label {
            font-size: 12px;
            color: #6b7280;
            width: 32px;
            flex-shrink: 0;
        }

        .progress-bar-container {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .progress-bar {
            flex: 1;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #8b5cf6;
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 12px;
            color: #6b7280;
            width: 32px;
            text-align: right;
            flex-shrink: 0;
        }

        /* AI洞察卡片 */
        .ai-insights {
            background: #fff;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            border-left: 4px solid #10b981;
        }

        .ai-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }

        .ai-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
        }

        .ai-icon {
            color: #10b981;
        }

        .insight-item {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 8px;
            font-size: 14px;
            line-height: 1.4;
        }

        .insight-item:last-child {
            margin-bottom: 0;
        }

        .insight-item.success {
            background: #ecfdf5;
            color: #065f46;
        }

        .insight-item.info {
            background: #eff6ff;
            color: #1e40af;
        }

        .insight-item.warning {
            background: #fffbeb;
            color: #92400e;
        }

        .insight-emoji {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-frame">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="time">9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <div class="wifi-icon">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div class="battery">
                        <div class="battery-level"></div>
                    </div>
                </div>
            </div>

            <!-- App Content -->
            <div class="app-content">
                <div class="insights-container">
                    <!-- 导航栏 -->
                    <div class="nav-header">
                        <div class="nav-left">
                            <button class="back-btn" onclick="history.back()" title="返回">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                            <div>
                                <div class="nav-title">数据洞察</div>
                                <div class="nav-subtitle">了解你的成长轨迹</div>
                            </div>
                        </div>
                        
                        <!-- 时间范围选择器 -->
                        <div class="time-selector">
                            <button class="time-btn active" onclick="switchTimeRange('week', this)">本周</button>
                            <button class="time-btn" onclick="switchTimeRange('month', this)">本月</button>
                            <button class="time-btn" onclick="switchTimeRange('year', this)">今年</button>
                        </div>
                    </div>

                    <!-- 主内容 -->
                    <div class="insights-content">
                        <!-- 关键指标 -->
                        <div class="metrics-grid">
                            <div class="metric-card green">
                                <div class="metric-header">
                                    <div class="metric-icon green">
                                        <i class="fas fa-target"></i>
                                    </div>
                                    <div class="metric-info">
                                        <div class="metric-label">任务完成率</div>
                                        <div class="metric-value" id="task-rate">80%</div>
                                    </div>
                                </div>
                            </div>

                            <div class="metric-card blue">
                                <div class="metric-header">
                                    <div class="metric-icon blue">
                                        <i class="fas fa-award"></i>
                                    </div>
                                    <div class="metric-info">
                                        <div class="metric-label">目标达成率</div>
                                        <div class="metric-value" id="goal-rate">33%</div>
                                    </div>
                                </div>
                            </div>

                            <div class="metric-card orange">
                                <div class="metric-header">
                                    <div class="metric-icon orange">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="metric-info">
                                        <div class="metric-label">专注时长</div>
                                        <div class="metric-value" id="focus-time">18.5h</div>
                                    </div>
                                </div>
                            </div>

                            <div class="metric-card purple">
                                <div class="metric-header">
                                    <div class="metric-icon purple">
                                        <i class="fas fa-fire"></i>
                                    </div>
                                    <div class="metric-info">
                                        <div class="metric-label">连续天数</div>
                                        <div class="metric-value" id="streak">5天</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 本周任务完成情况 -->
                        <div class="chart-card">
                            <div class="chart-header">
                                <i class="fas fa-chart-bar chart-icon"></i>
                                <div class="chart-title">本周任务完成情况</div>
                            </div>
                            <div class="weekly-progress" id="weekly-chart">
                                <div class="day-progress">
                                    <div class="day-label">周一</div>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 75%"></div>
                                        </div>
                                        <div class="progress-text">3/4</div>
                                    </div>
                                </div>
                                <div class="day-progress">
                                    <div class="day-label">周二</div>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 67%"></div>
                                        </div>
                                        <div class="progress-text">2/3</div>
                                    </div>
                                </div>
                                <div class="day-progress">
                                    <div class="day-label">周三</div>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 100%"></div>
                                        </div>
                                        <div class="progress-text">4/4</div>
                                    </div>
                                </div>
                                <div class="day-progress">
                                    <div class="day-label">周四</div>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 50%"></div>
                                        </div>
                                        <div class="progress-text">1/2</div>
                                    </div>
                                </div>
                                <div class="day-progress">
                                    <div class="day-label">周五</div>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 100%"></div>
                                        </div>
                                        <div class="progress-text">2/2</div>
                                    </div>
                                </div>
                                <div class="day-progress">
                                    <div class="day-label">周六</div>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 0%"></div>
                                        </div>
                                        <div class="progress-text">0/0</div>
                                    </div>
                                </div>
                                <div class="day-progress">
                                    <div class="day-label">周日</div>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar">
                                            <div class="progress-fill" style="width: 0%"></div>
                                        </div>
                                        <div class="progress-text">0/0</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI洞察建议 -->
                        <div class="ai-insights">
                            <div class="ai-header">
                                <i class="fas fa-brain ai-icon"></i>
                                <div class="ai-title">AI 洞察建议</div>
                            </div>
                            <div class="insight-item success">
                                <span class="insight-emoji">🎯</span>
                                <strong>表现优秀！</strong> 你的任务完成率比上周提高了15%，保持这个节奏！
                            </div>
                            <div class="insight-item info">
                                <span class="insight-emoji">⏰</span>
                                <strong>时间优化建议：</strong> 你在周三的效率最高，建议把重要任务安排在这一天。
                            </div>
                            <div class="insight-item warning">
                                <span class="insight-emoji">🔥</span>
                                <strong>连续打卡：</strong> 已连续5天完成任务，再坚持5天就能获得新成就！
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 数据存储
        const statsData = {
            week: {
                taskRate: 80,
                goalRate: 33,
                focusTime: '18.5h',
                streak: '5天',
                weeklyData: [
                    { day: '周一', completed: 3, total: 4 },
                    { day: '周二', completed: 2, total: 3 },
                    { day: '周三', completed: 4, total: 4 },
                    { day: '周四', completed: 1, total: 2 },
                    { day: '周五', completed: 2, total: 2 },
                    { day: '周六', completed: 0, total: 0 },
                    { day: '周日', completed: 0, total: 0 }
                ]
            },
            month: {
                taskRate: 80,
                goalRate: 67,
                focusTime: '72h',
                streak: '12天'
            },
            year: {
                taskRate: 82,
                goalRate: 67,
                focusTime: '320h',
                streak: '45天'
            }
        };

        // 切换时间范围
        function switchTimeRange(range, button) {
            // 更新按钮状态
            document.querySelectorAll('.time-btn').forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            
            // 更新数据
            const data = statsData[range];
            document.getElementById('task-rate').textContent = data.taskRate + '%';
            document.getElementById('goal-rate').textContent = data.goalRate + '%';
            document.getElementById('focus-time').textContent = data.focusTime;
            document.getElementById('streak').textContent = data.streak;
        }
    </script>
</body>
</html>
