<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>愿景详情 - 美好生活</title>
    <link rel="stylesheet" href="../assets/css/mobile-app.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .vision-container {
            height: 100%;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 顶部导航栏 */
        .nav-header {
            background: #fff;
            height: 88px;
            padding: 54px 20px 16px;
            border-bottom: 1px solid #e5e5e7;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 100;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: #e5e5ea;
            transform: scale(1.05);
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
        }

        .nav-actions {
            display: flex;
            gap: 8px;
        }

        .nav-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .nav-btn:hover {
            background: #e5e5ea;
            transform: scale(1.05);
        }

        /* 主内容区域 */
        .vision-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        /* 愿景卡片 */
        .vision-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 20px;
            padding: 24px;
            margin-bottom: 20px;
            color: #fff;
            position: relative;
            overflow: hidden;
        }

        .vision-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80') center/cover;
            opacity: 0.2;
            z-index: 0;
        }

        .vision-content-inner {
            position: relative;
            z-index: 1;
        }

        .vision-title {
            font-size: 24px;
            font-weight: 700;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .vision-description {
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 20px;
            opacity: 0.9;
        }

        .vision-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }

        /* 计划部分 */
        .plan-section {
            background: #fff;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 16px rgba(0, 0, 0, 0.08);
        }

        .section-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .progress-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .progress-badge.in-progress {
            background: #fff3cd;
            color: #856404;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f2f2f7;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007aff 0%, #34c759 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* 阶段管理样式 */
        .add-stage-btn {
            background: #8b5cf6;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.2s ease;
        }

        .add-stage-btn:hover {
            background: #7c3aed;
            transform: translateY(-1px);
        }

        .stage-container {
            background: #fff;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stage-container.completed {
            border-left: 4px solid #10b981;
        }

        .stage-container.active {
            border-left: 4px solid #3b82f6;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.15);
        }

        .stage-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 20px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #fafafa;
        }

        .stage-header:hover {
            background: #f5f5f5;
        }

        .stage-container.active .stage-header {
            background: #f0f7ff;
        }

        .stage-container.completed .stage-header {
            background: #f0fdf4;
        }

        .stage-info {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
        }

        .stage-status {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .stage-container.completed .stage-status {
            background: #dcfce7;
            color: #16a34a;
        }

        .stage-container.active .stage-status {
            background: #dbeafe;
            color: #2563eb;
        }

        .stage-container:not(.completed):not(.active) .stage-status {
            background: #f3f4f6;
            color: #6b7280;
        }

        .stage-details {
            flex: 1;
        }

        .stage-title {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .stage-description {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 8px;
        }

        .stage-progress {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .progress-text {
            font-size: 12px;
            color: #6b7280;
        }

        .mini-progress-bar {
            width: 80px;
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
        }

        .mini-progress-fill {
            height: 100%;
            background: #3b82f6;
            border-radius: 2px;
            transition: width 0.3s ease;
        }

        .stage-container.completed .mini-progress-fill {
            background: #10b981;
        }

        .stage-actions {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            border: none;
            background: #f3f4f6;
            color: #6b7280;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .action-btn:hover {
            background: #e5e7eb;
            color: #374151;
        }

        .expand-icon {
            color: #9ca3af;
            transition: transform 0.3s ease;
        }

        .stage-content.expanded ~ .stage-header .expand-icon {
            transform: rotate(180deg);
        }

        .stage-content {
            padding: 0 20px;
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stage-content.expanded {
            max-height: 1000px;
            padding: 20px;
        }

        /* 步骤样式 */
        .step-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
            padding: 12px 0;
            border-bottom: 1px solid #f3f4f6;
        }

        .step-item:last-of-type {
            border-bottom: none;
            margin-bottom: 16px;
        }

        .step-checkbox {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .step-item.completed .step-checkbox {
            background: #10b981;
            color: white;
        }

        .step-item.active .step-checkbox {
            background: #3b82f6;
            color: white;
        }

        .step-item:not(.completed):not(.active) .step-checkbox {
            background: #f3f4f6;
            color: #9ca3af;
        }

        .step-progress-ring {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: conic-gradient(#3b82f6 65%, #e5e7eb 65%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            font-weight: 600;
            color: #1f2937;
            position: relative;
        }

        .step-progress-ring::before {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            z-index: 1;
        }

        .step-progress-ring span {
            position: relative;
            z-index: 2;
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }

        .step-description {
            font-size: 13px;
            color: #6b7280;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .step-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .step-frequency {
            display: flex;
            align-items: center;
            gap: 6px;
            font-size: 12px;
            color: #6b7280;
        }

        .step-actions {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .step-action-btn {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            border: none;
            background: #f9fafb;
            color: #6b7280;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            transition: all 0.2s ease;
        }

        .step-action-btn:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .checkin-btn {
            background: #10b981;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 4px 8px;
            font-size: 11px;
            font-weight: 500;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;
            transition: all 0.2s ease;
        }

        .checkin-btn:hover {
            background: #059669;
            transform: translateY(-1px);
        }

        .add-step-btn {
            width: 100%;
            background: #f8fafc;
            border: 2px dashed #cbd5e1;
            border-radius: 8px;
            padding: 12px;
            color: #64748b;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .add-step-btn:hover {
            background: #f1f5f9;
            border-color: #94a3b8;
            color: #475569;
        }

        /* 快速操作 */
        .quick-actions {
            position: fixed;
            bottom: 34px;
            right: 20px;
            z-index: 100;
        }

        .fab {
            width: 56px;
            height: 56px;
            border-radius: 28px;
            background: #007aff;
            border: none;
            color: #fff;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 8px 32px rgba(0, 122, 255, 0.4);
            transition: all 0.3s ease;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 40px rgba(0, 122, 255, 0.6);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-frame">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="time">9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <div class="wifi-icon">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div class="battery">
                        <div class="battery-level"></div>
                    </div>
                </div>
            </div>

            <!-- App Content -->
            <div class="app-content">
                <div class="vision-container">
                    <!-- 导航栏 -->
                    <div class="nav-header">
                        <div class="nav-left">
                            <button class="back-btn" onclick="history.back()" title="返回">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                            <div class="nav-title">愿景详情</div>
                        </div>
                        <div class="nav-actions">
                            <button class="nav-btn" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="nav-btn" title="分享">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 主内容 -->
                    <div class="vision-content">
                        <!-- 愿景概览 -->
                        <div class="vision-card">
                            <div class="vision-content-inner">
                                <div class="vision-title">
                                    🏖️ 东南亚海边生活
                                </div>
                                <div class="vision-description">
                                    在30岁前在泰国或马来西亚海边拥有一套房子，实现远程工作的自由生活方式，每天可以在海边看日出日落，享受慢节奏的美好时光。
                                </div>
                                <div class="vision-stats">
                                    <div class="stat-item">
                                        <div class="stat-number">42%</div>
                                        <div class="stat-label">完成进度</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number">5.2年</div>
                                        <div class="stat-label">剩余时间</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number">3/7</div>
                                        <div class="stat-label">完成步骤</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 整体进度 -->
                        <div class="plan-section">
                            <div class="section-header">
                                <div class="section-title">
                                    <i class="fas fa-chart-line"></i>
                                    整体进度
                                </div>
                                <div class="progress-badge in-progress">进行中</div>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 42%"></div>
                            </div>
                            <p style="font-size: 14px; color: #8e8e93; margin: 8px 0 0;">
                                已完成 3 个关键步骤，还有 4 个步骤待完成
                            </p>
                        </div>

                        <!-- 实现阶段 -->
                        <div class="plan-section">
                            <div class="section-header">
                                <div class="section-title">
                                    <i class="fas fa-layer-group"></i>
                                    实现阶段
                                </div>
                                <button class="add-stage-btn" onclick="showAddStageModal()">
                                    <i class="fas fa-plus"></i>
                                    添加阶段
                                </button>
                            </div>

                            <!-- 阶段1：基础准备 -->
                            <div class="stage-container completed">
                                <div class="stage-header" onclick="toggleStage('stage1')">
                                    <div class="stage-info">
                                        <div class="stage-status">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                        <div class="stage-details">
                                            <div class="stage-title">阶段1：基础准备</div>
                                            <div class="stage-description">提升技能和建立财务基础</div>
                                            <div class="stage-progress">
                                                <span class="progress-text">已完成 2/2 步骤</span>
                                                <div class="mini-progress-bar">
                                                    <div class="mini-progress-fill" style="width: 100%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="stage-actions">
                                        <button class="action-btn" onclick="editStage('stage1')" title="编辑阶段">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <i class="fas fa-chevron-down expand-icon"></i>
                                    </div>
                                </div>

                                <div class="stage-content" id="stage1">
                                    <div class="step-item completed">
                                        <div class="step-checkbox">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="step-content">
                                            <div class="step-title">提升技能和收入</div>
                                            <div class="step-description">学习新技术栈，争取加薪或跳槽到更高薪职位</div>
                                            <div class="step-meta">
                                                <div class="step-frequency">
                                                    <i class="fas fa-calendar-check"></i>
                                                    <span>已完成</span>
                                                </div>
                                                <div class="step-actions">
                                                    <button class="step-action-btn" onclick="editStep('step1')" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="step-item completed">
                                        <div class="step-checkbox">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="step-content">
                                            <div class="step-title">建立应急基金</div>
                                            <div class="step-description">存储6个月生活费作为应急基金</div>
                                            <div class="step-meta">
                                                <div class="step-frequency">
                                                    <i class="fas fa-calendar-check"></i>
                                                    <span>已完成</span>
                                                </div>
                                                <div class="step-actions">
                                                    <button class="step-action-btn" onclick="editStep('step2')" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 阶段2：投资学习 -->
                            <div class="stage-container active">
                                <div class="stage-header" onclick="toggleStage('stage2')">
                                    <div class="stage-info">
                                        <div class="stage-status">
                                            <i class="fas fa-play-circle"></i>
                                        </div>
                                        <div class="stage-details">
                                            <div class="stage-title">阶段2：投资学习</div>
                                            <div class="stage-description">学习理财知识，开始投资实践</div>
                                            <div class="stage-progress">
                                                <span class="progress-text">进行中 2/3 步骤</span>
                                                <div class="mini-progress-bar">
                                                    <div class="mini-progress-fill" style="width: 67%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="stage-actions">
                                        <button class="action-btn" onclick="editStage('stage2')" title="编辑阶段">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <i class="fas fa-chevron-down expand-icon"></i>
                                    </div>
                                </div>

                                <div class="stage-content expanded" id="stage2">
                                    <div class="step-item completed">
                                        <div class="step-checkbox">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <div class="step-content">
                                            <div class="step-title">学习基础理财知识</div>
                                            <div class="step-description">阅读理财书籍，了解基金、股票基础知识</div>
                                            <div class="step-meta">
                                                <div class="step-frequency">
                                                    <i class="fas fa-book"></i>
                                                    <span>每周阅读 - 已完成</span>
                                                </div>
                                                <div class="step-actions">
                                                    <button class="step-action-btn" onclick="viewCheckIns('step3')" title="查看打卡">
                                                        <i class="fas fa-calendar-alt"></i>
                                                    </button>
                                                    <button class="step-action-btn" onclick="editStep('step3')" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="step-item active">
                                        <div class="step-checkbox">
                                            <div class="step-progress-ring">
                                                <span>65%</span>
                                            </div>
                                        </div>
                                        <div class="step-content">
                                            <div class="step-title">开始小额投资实践</div>
                                            <div class="step-description">每月投资1000元到指数基金，学习实际操作</div>
                                            <div class="step-meta">
                                                <div class="step-frequency">
                                                    <i class="fas fa-calendar-week"></i>
                                                    <span>每月投资 - 进行中</span>
                                                </div>
                                                <div class="step-actions">
                                                    <button class="checkin-btn" onclick="showCheckInModal('step4')" title="打卡">
                                                        <i class="fas fa-check-circle"></i>
                                                        打卡
                                                    </button>
                                                    <button class="step-action-btn" onclick="viewCheckIns('step4')" title="查看打卡">
                                                        <i class="fas fa-calendar-alt"></i>
                                                    </button>
                                                    <button class="step-action-btn" onclick="editStep('step4')" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="step-item">
                                        <div class="step-checkbox">
                                            <i class="fas fa-circle"></i>
                                        </div>
                                        <div class="step-content">
                                            <div class="step-title">制定投资策略</div>
                                            <div class="step-description">根据学习成果制定长期投资策略和资产配置方案</div>
                                            <div class="step-meta">
                                                <div class="step-frequency">
                                                    <i class="fas fa-calendar-day"></i>
                                                    <span>一次性任务</span>
                                                </div>
                                                <div class="step-actions">
                                                    <button class="step-action-btn" onclick="editStep('step5')" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <button class="add-step-btn" onclick="showAddStepModal('stage2')">
                                        <i class="fas fa-plus"></i>
                                        添加步骤
                                    </button>
                                </div>
                            </div>

                            <!-- 阶段3：资金积累 -->
                            <div class="stage-container">
                                <div class="stage-header" onclick="toggleStage('stage3')">
                                    <div class="stage-info">
                                        <div class="stage-status">
                                            <i class="fas fa-pause-circle"></i>
                                        </div>
                                        <div class="stage-details">
                                            <div class="stage-title">阶段3：资金积累</div>
                                            <div class="stage-description">积累房产首付和相关费用</div>
                                            <div class="stage-progress">
                                                <span class="progress-text">待开始 0/2 步骤</span>
                                                <div class="mini-progress-bar">
                                                    <div class="mini-progress-fill" style="width: 0%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="stage-actions">
                                        <button class="action-btn" onclick="editStage('stage3')" title="编辑阶段">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <i class="fas fa-chevron-right expand-icon"></i>
                                    </div>
                                </div>

                                <div class="stage-content" id="stage3">
                                    <div class="step-item">
                                        <div class="step-checkbox">
                                            <i class="fas fa-circle"></i>
                                        </div>
                                        <div class="step-content">
                                            <div class="step-title">积累首付资金</div>
                                            <div class="step-description">通过工作收入和投资收益积累房产首付</div>
                                            <div class="step-meta">
                                                <div class="step-frequency">
                                                    <i class="fas fa-calendar-week"></i>
                                                    <span>每月储蓄</span>
                                                </div>
                                                <div class="step-actions">
                                                    <button class="step-action-btn" onclick="editStep('step6')" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="step-item">
                                        <div class="step-checkbox">
                                            <i class="fas fa-circle"></i>
                                        </div>
                                        <div class="step-content">
                                            <div class="step-title">研究目标地区房价</div>
                                            <div class="step-description">定期关注泰国和马来西亚的房价走势和政策变化</div>
                                            <div class="step-meta">
                                                <div class="step-frequency">
                                                    <i class="fas fa-calendar-week"></i>
                                                    <span>每周研究</span>
                                                </div>
                                                <div class="step-actions">
                                                    <button class="step-action-btn" onclick="editStep('step7')" title="编辑">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <button class="add-step-btn" onclick="showAddStepModal('stage3')">
                                        <i class="fas fa-plus"></i>
                                        添加步骤
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速操作 -->
                    <div class="quick-actions">
                        <button class="fab" title="添加步骤">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
