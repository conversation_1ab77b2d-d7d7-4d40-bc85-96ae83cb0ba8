<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>历史对话 - 美好生活</title>
    <link rel="stylesheet" href="../assets/css/mobile-app.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .history-container {
            height: 100%;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 顶部导航栏 */
        .nav-header {
            background: #fff;
            height: 88px;
            padding: 54px 20px 16px;
            border-bottom: 1px solid #e5e5e7;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 100;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .back-btn:hover {
            background: #e5e5ea;
            transform: scale(1.05);
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
        }

        .nav-actions {
            display: flex;
            gap: 8px;
        }

        .nav-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .nav-btn:hover {
            background: #e5e5ea;
            transform: scale(1.05);
        }

        /* 搜索栏 */
        .search-bar {
            background: #fff;
            padding: 16px 20px;
            border-bottom: 1px solid #f2f2f7;
        }

        .search-input {
            width: 100%;
            height: 36px;
            background: #f2f2f7;
            border: none;
            border-radius: 18px;
            padding: 0 16px 0 40px;
            font-size: 16px;
            color: #1d1d1f;
            outline: none;
        }

        .search-input::placeholder {
            color: #8e8e93;
        }

        .search-wrapper {
            position: relative;
        }

        .search-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #8e8e93;
            font-size: 14px;
        }

        /* 主内容区域 */
        .history-content {
            flex: 1;
            overflow-y: auto;
            padding: 0;
        }

        /* 对话列表 */
        .conversation-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .conversation-item {
            background: #fff;
            border-bottom: 1px solid #f2f2f7;
            padding: 16px 20px;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .conversation-item:hover {
            background: #f8f9fa;
        }

        .conversation-item:last-child {
            border-bottom: none;
        }

        .conversation-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .conversation-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .conversation-date {
            font-size: 12px;
            color: #8e8e93;
        }

        .conversation-preview {
            font-size: 14px;
            color: #8e8e93;
            line-height: 1.4;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .conversation-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            color: #8e8e93;
        }

        .conversation-stats {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        /* 话题标签 */
        .topic-tag {
            background: #007aff;
            color: #fff;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
        }

        .topic-tag.finance {
            background: #34c759;
        }

        .topic-tag.career {
            background: #ff9500;
        }

        .topic-tag.lifestyle {
            background: #af52de;
        }

        .topic-tag.planning {
            background: #ff2d92;
        }

        /* 分组标题 */
        .group-header {
            background: #f8f9fa;
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 600;
            color: #8e8e93;
            border-bottom: 1px solid #f2f2f7;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #8e8e93;
        }

        .empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-description {
            font-size: 14px;
            line-height: 1.4;
        }

        /* 快速操作按钮 */
        .quick-actions {
            position: fixed;
            bottom: 34px;
            right: 20px;
            z-index: 100;
        }

        .fab {
            width: 56px;
            height: 56px;
            border-radius: 28px;
            background: #007aff;
            border: none;
            color: #fff;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 8px 32px rgba(0, 122, 255, 0.4);
            transition: all 0.3s ease;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 40px rgba(0, 122, 255, 0.6);
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-frame">
            <!-- Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="time">9:41</span>
                </div>
                <div class="status-right">
                    <div class="signal-bars">
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                        <div class="bar"></div>
                    </div>
                    <div class="wifi-icon">
                        <i class="fas fa-wifi"></i>
                    </div>
                    <div class="battery">
                        <div class="battery-level"></div>
                    </div>
                </div>
            </div>

            <!-- App Content -->
            <div class="app-content">
                <div class="history-container">
                    <!-- 导航栏 -->
                    <div class="nav-header">
                        <div class="nav-left">
                            <button class="back-btn" onclick="history.back()" title="返回">
                                <i class="fas fa-arrow-left"></i>
                            </button>
                            <div class="nav-title">历史对话</div>
                        </div>
                        <div class="nav-actions">
                            <button class="nav-btn" title="筛选">
                                <i class="fas fa-filter"></i>
                            </button>
                            <button class="nav-btn" title="更多">
                                <i class="fas fa-ellipsis-h"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 搜索栏 -->
                    <div class="search-bar">
                        <div class="search-wrapper">
                            <i class="fas fa-search search-icon"></i>
                            <input type="text" class="search-input" placeholder="搜索对话内容...">
                        </div>
                    </div>

                    <!-- 主内容 -->
                    <div class="history-content">
                        <!-- 今天 -->
                        <div class="group-header">今天</div>
                        <ul class="conversation-list">
                            <li class="conversation-item">
                                <div class="conversation-header">
                                    <div class="conversation-title">
                                        🏖️ 东南亚海边生活规划
                                        <span class="topic-tag planning">规划</span>
                                    </div>
                                    <div class="conversation-date">14:32</div>
                                </div>
                                <div class="conversation-preview">
                                    我想在30岁前在东南亚海边拥有房子，实现远程工作的自由生活。AI帮我制定了详细的6-7年实现计划...
                                </div>
                                <div class="conversation-meta">
                                    <div class="conversation-stats">
                                        <div class="stat-item">
                                            <i class="fas fa-comment"></i>
                                            <span>23条</span>
                                        </div>
                                        <div class="stat-item">
                                            <i class="fas fa-clock"></i>
                                            <span>45分钟</span>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>

                        <!-- 昨天 -->
                        <div class="group-header">昨天</div>
                        <ul class="conversation-list">
                            <li class="conversation-item">
                                <div class="conversation-header">
                                    <div class="conversation-title">
                                        💰 投资理财学习计划
                                        <span class="topic-tag finance">理财</span>
                                    </div>
                                    <div class="conversation-date">昨天 19:15</div>
                                </div>
                                <div class="conversation-preview">
                                    想学习基金和股票投资，但不知道从哪里开始。AI推荐了系统的学习路径和实践方案...
                                </div>
                                <div class="conversation-meta">
                                    <div class="conversation-stats">
                                        <div class="stat-item">
                                            <i class="fas fa-comment"></i>
                                            <span>18条</span>
                                        </div>
                                        <div class="stat-item">
                                            <i class="fas fa-clock"></i>
                                            <span>32分钟</span>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="conversation-item">
                                <div class="conversation-header">
                                    <div class="conversation-title">
                                        🚀 职业发展规划
                                        <span class="topic-tag career">职业</span>
                                    </div>
                                    <div class="conversation-date">昨天 10:22</div>
                                </div>
                                <div class="conversation-preview">
                                    讨论了技术栈学习和职业晋升路径，AI建议了具体的技能提升方案和时间安排...
                                </div>
                                <div class="conversation-meta">
                                    <div class="conversation-stats">
                                        <div class="stat-item">
                                            <i class="fas fa-comment"></i>
                                            <span>15条</span>
                                        </div>
                                        <div class="stat-item">
                                            <i class="fas fa-clock"></i>
                                            <span>28分钟</span>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <!-- 快速操作 -->
                    <div class="quick-actions">
                        <button class="fab" title="新建对话">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
