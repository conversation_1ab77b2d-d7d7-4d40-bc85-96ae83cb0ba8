<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>美好生活 - AI愿景助手</title>
    <link rel="stylesheet" href="../assets/css/mobile-app.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .home-container {
            height: 100%;
            background: #f8f9fa;
            display: flex;
            position: relative;
            overflow: hidden;
        }

        /* 侧边栏 */
        .sidebar {
            width: 0;
            background: #fff;
            border-right: 1px solid #e5e5e7;
            transition: width 0.3s ease;
            overflow: hidden;
            z-index: 150;
            position: relative;
        }

        .sidebar.open {
            width: 280px;
        }

        .sidebar-content {
            width: 280px;
            height: 100%;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar-header {
            text-align: center;
            margin-bottom: 24px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f2f2f7;
        }

        .user-avatar {
            width: 80px;
            height: 80px;
            border-radius: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 12px;
            color: #fff;
            font-size: 32px;
        }

        .user-name {
            font-size: 18px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 4px;
        }

        .user-status {
            font-size: 14px;
            color: #8e8e93;
        }

        .sidebar-section {
            margin-bottom: 24px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .vision-summary {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 12px;
            padding: 16px;
            color: #fff;
            margin-bottom: 16px;
        }

        .vision-summary h4 {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
            opacity: 0.9;
        }

        .vision-summary p {
            font-size: 13px;
            line-height: 1.4;
            opacity: 0.85;
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .menu-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            border-radius: 8px;
            color: #1d1d1f;
            text-decoration: none;
            transition: background-color 0.2s ease;
            margin-bottom: 4px;
            cursor: pointer;
        }

        .menu-item:hover {
            background: #f2f2f7;
        }

        .menu-item i {
            width: 20px;
            margin-right: 12px;
            color: #8e8e93;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        /* 顶部导航栏 */
        .nav-header {
            background: #fff;
            height: 88px;
            padding: 54px 20px 16px;
            border-bottom: 1px solid #e5e5e7;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            z-index: 100;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar-toggle {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .sidebar-toggle:hover {
            background: #e5e5ea;
            transform: scale(1.05);
        }

        .nav-title {
            font-size: 20px;
            font-weight: 600;
            color: #1d1d1f;
            letter-spacing: -0.3px;
        }

        .nav-actions {
            display: flex;
            gap: 16px;
        }

        .nav-btn {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: #f2f2f7;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #007aff;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .nav-btn:hover {
            background: #e5e5ea;
            transform: scale(1.05);
        }

        /* 对话区域 */
        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 16px 20px 80px;
            scroll-behavior: smooth;
        }

        .chat-message {
            margin-bottom: 24px;
            animation: fadeInUp 0.4s ease-out;
        }

        .message-user {
            display: flex;
            justify-content: flex-end;
        }

        .message-ai {
            display: flex;
            justify-content: flex-start;
        }

        .message-bubble {
            max-width: 280px;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 16px;
            line-height: 1.4;
            position: relative;
        }

        .user-bubble {
            background: #007aff;
            color: #fff;
            border-bottom-right-radius: 6px;
        }

        .ai-bubble {
            background: #fff;
            color: #1d1d1f;
            border: 1px solid #e5e5e7;
            border-bottom-left-radius: 6px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .message-time {
            font-size: 12px;
            color: #8e8e93;
            text-align: center;
            margin: 8px 0 16px;
        }

        /* AI头像 */
        .ai-avatar {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 8px;
            margin-top: 4px;
            flex-shrink: 0;
        }

        .ai-avatar i {
            color: #fff;
            font-size: 14px;
        }

        /* 特殊消息类型 */
        .vision-card {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 16px;
            padding: 16px;
            margin: 16px 0;
            color: #fff;
        }

        .vision-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .vision-content {
            font-size: 15px;
            line-height: 1.5;
            opacity: 0.95;
        }

        .steps-list {
            background: #f2f2f7;
            border-radius: 12px;
            padding: 16px;
            margin: 12px 0;
        }

        .step-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            font-size: 15px;
            line-height: 1.4;
        }

        .step-item:last-child {
            margin-bottom: 0;
        }

        .step-number {
            width: 24px;
            height: 24px;
            border-radius: 12px;
            background: #007aff;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .todo-list {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 12px;
            padding: 16px;
            margin: 12px 0;
        }

        .todo-title {
            font-size: 16px;
            font-weight: 600;
            color: #856404;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .todo-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
            color: #856404;
        }

        .todo-item:last-child {
            margin-bottom: 0;
        }

        .todo-checkbox {
            width: 16px;
            height: 16px;
            border: 2px solid #ffc107;
            border-radius: 3px;
            margin-right: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .todo-checkbox.checked {
            background: #ffc107;
            color: #fff;
        }

        /* 输入区域 */
        .input-container {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: #fff;
            border-top: 1px solid #e5e5e7;
            padding: 12px 20px 34px;
            z-index: 200;
        }

        .input-wrapper {
            display: flex;
            align-items: flex-end;
            gap: 12px;
            max-width: 393px;
            margin: 0 auto;
        }

        .message-input {
            flex: 1;
            min-height: 36px;
            max-height: 120px;
            padding: 8px 16px;
            border: 1px solid #e5e5e7;
            border-radius: 18px;
            font-size: 16px;
            line-height: 1.4;
            resize: none;
            outline: none;
            background: #f8f9fa;
            transition: all 0.2s ease;
        }

        .message-input:focus {
            border-color: #007aff;
            background: #fff;
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
        }

        .send-btn {
            width: 36px;
            height: 36px;
            border-radius: 18px;
            background: #007aff;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.2s ease;
            flex-shrink: 0;
        }

        .send-btn:hover {
            background: #0056b3;
            transform: scale(1.05);
        }

        .send-btn:disabled {
            background: #c7c7cc;
            cursor: not-allowed;
            transform: none;
        }

        /* 打字指示器 */
        .typing-indicator {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: #fff;
            border: 1px solid #e5e5e7;
            border-radius: 18px;
            border-bottom-left-radius: 6px;
            max-width: 280px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #c7c7cc;
            animation: typingPulse 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: 0s; }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typingPulse {
            0%, 60%, 100% {
                transform: scale(1);
                opacity: 0.5;
            }
            30% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 滚动条样式 */
        .chat-container::-webkit-scrollbar {
            width: 4px;
        }

        .chat-container::-webkit-scrollbar-track {
            background: transparent;
        }

        .chat-container::-webkit-scrollbar-thumb {
            background: #c7c7cc;
            border-radius: 2px;
        }

        .chat-container::-webkit-scrollbar-thumb:hover {
            background: #a1a1a6;
        }
    </style>
</head>
<body>
    <div class="iphone-container">
        <div class="iphone-screen">
            <!-- iOS Status Bar -->
            <div class="status-bar">
                <div class="status-left">
                    <span class="time">9:41</span>
                </div>
                <div class="status-center">
                    <div class="dynamic-island"></div>
                </div>
                <div class="status-right">
                    <div class="status-icons">
                        <div class="cellular-signal">
                            <div class="signal-dot"></div>
                            <div class="signal-dot"></div>
                            <div class="signal-dot"></div>
                            <div class="signal-dot"></div>
                        </div>
                        <div class="wifi-signal">
                            <svg width="15" height="11" viewBox="0 0 15 11" fill="none">
                                <path d="M7.5 0C11.64 0 15 1.79 15 4v3c0 2.21-3.36 4-7.5 4S0 9.21 0 7V4c0-2.21 3.36-4 7.5-4z" fill="currentColor" opacity="0.3"/>
                                <path d="M7.5 2C10.26 2 12.5 3.12 12.5 4.5v2c0 1.38-2.24 2.5-5 2.5s-5-1.12-5-2.5v-2C2.5 3.12 4.74 2 7.5 2z" fill="currentColor" opacity="0.6"/>
                                <path d="M7.5 4C9.16 4 10.5 4.67 10.5 5.5v1c0 0.83-1.34 1.5-3 1.5s-3-0.67-3-1.5v-1C4.5 4.67 5.84 4 7.5 4z" fill="currentColor"/>
                            </svg>
                        </div>
                        <div class="battery-indicator">
                            <div class="battery-body">
                                <div class="battery-level"></div>
                            </div>
                            <div class="battery-tip"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- App Content -->
            <div class="app-content">
                <div class="home-container">
                    <!-- 侧边栏 -->
                    <div class="sidebar" id="sidebar">
                        <div class="sidebar-content">
                            <!-- 用户信息 -->
                            <div class="sidebar-header">
                                <div class="user-avatar">
                                    <i class="fas fa-user"></i>
                                </div>
                                <div class="user-name">张小明</div>
                                <div class="user-status">25岁 · 程序员</div>
                            </div>

                            <!-- 当前愿景 -->
                            <div class="sidebar-section">
                                <div class="section-title">
                                    <i class="fas fa-star"></i>
                                    当前愿景
                                </div>
                                <div class="vision-summary">
                                    <h4>🏖️ 东南亚海边生活</h4>
                                    <p>在30岁前在泰国或马来西亚购买海景房，实现远程工作的自由生活</p>
                                </div>
                            </div>

                            <!-- 功能菜单 -->
                            <div class="sidebar-section">
                                <div class="section-title">
                                    <i class="fas fa-th-large"></i>
                                    功能
                                </div>
                                <ul class="sidebar-menu">
                                    <li class="menu-item">
                                        <i class="fas fa-plus"></i>
                                        新建愿景
                                    </li>
                                    <li class="menu-item">
                                        <i class="fas fa-list-check"></i>
                                        我的计划
                                    </li>
                                    <li class="menu-item">
                                        <i class="fas fa-chart-line"></i>
                                        进度追踪
                                    </li>
                                    <li class="menu-item">
                                        <i class="fas fa-clock"></i>
                                        历史对话
                                    </li>
                                    <li class="menu-item">
                                        <i class="fas fa-cog"></i>
                                        设置
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- 主内容区域 -->
                    <div class="main-content">
                        <!-- 导航栏 -->
                        <div class="nav-header">
                            <div class="nav-left">
                                <button class="sidebar-toggle" id="sidebarToggle" title="菜单">
                                    <i class="fas fa-bars"></i>
                                </button>
                                <div class="nav-title">美好生活助手</div>
                            </div>
                            <div class="nav-actions">
                                <button class="nav-btn" title="新对话">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="nav-btn" title="历史记录">
                                    <i class="fas fa-clock"></i>
                                </button>
                            </div>
                        </div>

                        <!-- 对话区域 -->
                        <div class="chat-container" id="chatContainer">
                            <!-- 对话内容将通过JavaScript动态加载 -->
                        </div>

                        <!-- 输入区域 -->
                        <div class="input-container">
                            <div class="input-wrapper">
                                <textarea
                                    class="message-input"
                                    id="messageInput"
                                    placeholder="描述你的生活愿景..."
                                    rows="1"
                                ></textarea>
                                <button class="send-btn" id="sendBtn">
                                    <i class="fas fa-arrow-up"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="../assets/js/chat.js"></script>
</body>
</html>
