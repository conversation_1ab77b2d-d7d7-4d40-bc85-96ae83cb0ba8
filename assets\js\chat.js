// 美好生活 AI 对话系统
class ChatApp {
    constructor() {
        this.chatContainer = document.getElementById('chatContainer');
        this.messageInput = document.getElementById('messageInput');
        this.sendBtn = document.getElementById('sendBtn');
        this.isTyping = false;
        
        this.initializeChat();
        this.bindEvents();
    }

    // 模拟对话历史数据
    chatHistory = [
        {
            type: 'ai',
            content: '你好！我是你的美好生活助手 ✨ 我可以帮你规划和实现生活愿景。请告诉我你想要实现什么样的美好生活？',
            time: '09:30'
        },
        {
            type: 'user',
            content: '我想在30岁之前在东南亚的海边拥有一套房子，可以远程工作，享受阳光沙滩的生活',
            time: '09:32'
        },
        {
            type: 'ai',
            content: '这是一个很棒的愿景！🏖️ 为了帮你制定更精确的计划，我需要了解一些信息：',
            time: '09:32'
        },
        {
            type: 'ai',
            content: '1. 你目前多少岁？\n2. 现在的职业是什么？\n3. 每月大概能存多少钱？\n4. 有偏好的东南亚国家吗？\n5. 对房子有什么具体要求？',
            time: '09:33'
        },
        {
            type: 'user',
            content: '我现在25岁，是一名程序员，月薪2万，每月能存8000左右。比较喜欢泰国和马来西亚，希望房子靠近海边，有2-3个房间',
            time: '09:35'
        },
        {
            type: 'ai',
            content: '太好了！基于你的情况，我为你制定了一个5年实现计划：',
            time: '09:36',
            special: 'vision',
            visionData: {
                title: '🏖️ 东南亚海边生活愿景',
                content: '在30岁前在泰国普吉岛或马来西亚槟城购买海景房，实现远程工作的自由生活方式'
            }
        },
        {
            type: 'ai',
            content: '实现步骤：',
            time: '09:36',
            special: 'steps',
            steps: [
                '建立紧急基金：存够6个月生活费（约12万）',
                '学习投资理财：将部分储蓄投资增值',
                '提升远程工作技能：掌握更多编程语言和项目管理',
                '研究目标国家：了解房产政策、签证要求',
                '实地考察：安排1-2次东南亚看房之旅',
                '申请房贷：准备首付和贷款材料',
                '完成购房：选定房产并办理相关手续'
            ]
        },
        {
            type: 'user',
            content: '这个计划看起来不错，但我担心5年时间会不会太紧张？而且我对投资理财不太懂',
            time: '09:40'
        },
        {
            type: 'ai',
            content: '你的担心很合理！让我调整一下计划，延长到6-7年，并加强理财学习部分：',
            time: '09:41'
        },
        {
            type: 'ai',
            content: '调整后的计划更加稳健，我们可以从基础的理财知识开始学习。',
            time: '09:41',
            special: 'todo',
            todos: [
                { text: '开设理财账户，开始定投基金', checked: false },
                { text: '每周学习1小时投资理财知识', checked: false },
                { text: '研究泰国和马来西亚房产市场', checked: true },
                { text: '提升英语水平，准备海外生活', checked: false },
                { text: '建立海外收入来源渠道', checked: false }
            ]
        }
    ];

    initializeChat() {
        this.renderChatHistory();
        this.scrollToBottom();
    }

    bindEvents() {
        // 发送按钮点击事件
        this.sendBtn.addEventListener('click', () => this.sendMessage());

        // 输入框回车事件
        this.messageInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });

        // 输入框自动调整高度
        this.messageInput.addEventListener('input', () => {
            this.adjustTextareaHeight();
            this.updateSendButton();
        });

        // 移动端键盘处理
        this.messageInput.addEventListener('focus', () => {
            this.handleKeyboardShow();
        });

        this.messageInput.addEventListener('blur', () => {
            this.handleKeyboardHide();
        });

        // 触摸优化
        this.addTouchOptimizations();

        // 初始化发送按钮状态
        this.updateSendButton();
    }

    handleKeyboardShow() {
        // 移动端键盘弹出时的处理
        setTimeout(() => {
            this.scrollToBottom();
        }, 300);
    }

    handleKeyboardHide() {
        // 移动端键盘收起时的处理
        setTimeout(() => {
            this.scrollToBottom();
        }, 100);
    }

    addTouchOptimizations() {
        // 防止双击缩放
        document.addEventListener('touchstart', (e) => {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        });

        // 优化滚动体验
        this.chatContainer.addEventListener('touchstart', () => {
            this.chatContainer.style.webkitOverflowScrolling = 'touch';
        });

        // 长按消息复制功能
        this.chatContainer.addEventListener('contextmenu', (e) => {
            const messageBubble = e.target.closest('.message-bubble');
            if (messageBubble) {
                e.preventDefault();
                this.showCopyMenu(messageBubble, e);
            }
        });
    }

    showCopyMenu(messageBubble, event) {
        // 简单的复制功能实现
        const text = messageBubble.textContent;
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(() => {
                this.showToast('消息已复制');
            });
        }
    }

    showToast(message) {
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 20px;
            border-radius: 20px;
            font-size: 14px;
            z-index: 10000;
            pointer-events: none;
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 2000);
    }

    renderChatHistory() {
        this.chatContainer.innerHTML = '';
        
        this.chatHistory.forEach((message, index) => {
            setTimeout(() => {
                this.addMessage(message);
            }, index * 200); // 逐条显示消息
        });
    }

    addMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `chat-message message-${message.type}`;

        if (message.type === 'user') {
            messageDiv.innerHTML = `
                <div class="message-bubble user-bubble">
                    ${this.formatMessage(message.content)}
                </div>
            `;
        } else {
            let content = '';
            
            if (message.special === 'vision') {
                content = `
                    <div class="ai-avatar">
                        <i class="fas fa-sparkles"></i>
                    </div>
                    <div>
                        <div class="message-bubble ai-bubble">
                            ${this.formatMessage(message.content)}
                        </div>
                        <div class="vision-card">
                            <div class="vision-title">${message.visionData.title}</div>
                            <div class="vision-content">${message.visionData.content}</div>
                        </div>
                    </div>
                `;
            } else if (message.special === 'steps') {
                const stepsHtml = message.steps.map((step, index) => `
                    <div class="step-item">
                        <div class="step-number">${index + 1}</div>
                        <div>${step}</div>
                    </div>
                `).join('');
                
                content = `
                    <div class="ai-avatar">
                        <i class="fas fa-list-check"></i>
                    </div>
                    <div>
                        <div class="message-bubble ai-bubble">
                            ${this.formatMessage(message.content)}
                        </div>
                        <div class="steps-list">
                            ${stepsHtml}
                        </div>
                    </div>
                `;
            } else if (message.special === 'todo') {
                const todosHtml = message.todos.map(todo => `
                    <div class="todo-item">
                        <div class="todo-checkbox ${todo.checked ? 'checked' : ''}">
                            ${todo.checked ? '<i class="fas fa-check"></i>' : ''}
                        </div>
                        <div>${todo.text}</div>
                    </div>
                `).join('');
                
                content = `
                    <div class="ai-avatar">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div>
                        <div class="message-bubble ai-bubble">
                            ${this.formatMessage(message.content)}
                        </div>
                        <div class="todo-list">
                            <div class="todo-title">
                                <i class="fas fa-clipboard-list"></i>
                                当前待办事项
                            </div>
                            ${todosHtml}
                        </div>
                    </div>
                `;
            } else {
                content = `
                    <div class="ai-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-bubble ai-bubble">
                        ${this.formatMessage(message.content)}
                    </div>
                `;
            }
            
            messageDiv.innerHTML = content;
        }

        // 添加时间戳
        if (message.time) {
            const timeDiv = document.createElement('div');
            timeDiv.className = 'message-time';
            timeDiv.textContent = message.time;
            this.chatContainer.appendChild(timeDiv);
        }

        this.chatContainer.appendChild(messageDiv);
        this.scrollToBottom();
    }

    formatMessage(content) {
        return content.replace(/\n/g, '<br>');
    }

    sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message || this.isTyping) return;

        // 添加用户消息
        const userMessage = {
            type: 'user',
            content: message,
            time: this.getCurrentTime()
        };
        
        this.addMessage(userMessage);
        this.messageInput.value = '';
        this.adjustTextareaHeight();
        this.updateSendButton();

        // 模拟AI回复
        this.simulateAIResponse(message);
    }

    simulateAIResponse(userMessage) {
        this.isTyping = true;
        
        // 显示打字指示器
        setTimeout(() => {
            this.showTypingIndicator();
        }, 500);

        // 模拟AI回复
        setTimeout(() => {
            this.hideTypingIndicator();
            
            const aiResponse = this.generateAIResponse(userMessage);
            this.addMessage(aiResponse);
            
            this.isTyping = false;
        }, 2000);
    }

    generateAIResponse(userMessage) {
        const responses = [
            "这是一个很好的想法！让我帮你分析一下具体的实现方案。",
            "我理解你的需求，让我为你制定一个详细的行动计划。",
            "基于你提供的信息，我建议我们从以下几个方面开始：",
            "这个愿景很棒！我们需要考虑一些实际的因素来制定可行的计划。"
        ];

        return {
            type: 'ai',
            content: responses[Math.floor(Math.random() * responses.length)],
            time: this.getCurrentTime()
        };
    }

    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.className = 'chat-message message-ai';
        typingDiv.id = 'typing-indicator';
        typingDiv.innerHTML = `
            <div class="ai-avatar">
                <i class="fas fa-robot"></i>
            </div>
            <div class="typing-indicator">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        `;
        
        this.chatContainer.appendChild(typingDiv);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typing-indicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    adjustTextareaHeight() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }

    updateSendButton() {
        const hasContent = this.messageInput.value.trim().length > 0;
        this.sendBtn.disabled = !hasContent || this.isTyping;
    }

    scrollToBottom() {
        setTimeout(() => {
            this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
        }, 100);
    }

    getCurrentTime() {
        const now = new Date();
        return now.getHours().toString().padStart(2, '0') + ':' + 
               now.getMinutes().toString().padStart(2, '0');
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ChatApp();
});
